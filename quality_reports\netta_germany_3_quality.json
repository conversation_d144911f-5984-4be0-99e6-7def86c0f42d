{"image_path": "expense_files\\netta_germany_3.png", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.5, "image_subtype": "digital_document", "indicators": {"low_color_variety": true, "matches_common_resolution": false, "has_regular_edges": "False", "has_uniform_background": false, "has_many_straight_lines": true, "low_histogram_entropy": "True"}, "metadata": {"unique_colors": 2531, "edge_density": 0.071, "histogram_entropy": "1.9"}}, "timestamp": "2025-07-16T12:54:02.107480", "processing_time_seconds": 0.71, "overall_assessment": {"score": 72.0, "level": "Acceptable", "pass_fail": true, "issues_summary": ["Severe glare detected"], "recommendations": ["📱 This is a digital_document - physical document checks have been adjusted.", "⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "🔄 Image quality insufficient. Please capture again with better camera/scanner settings.", "✅ Image sharpness is excellent."]}, "detailed_results": {"resolution": {"dimensions": {"width": 624, "height": 736, "megapixels": 0.46}, "dpi": {"horizontal": 200.0, "vertical": 87.0, "average": 143.0}, "quality": {"score": 60.0, "level": "Digital Quality", "meets_ocr_requirements": false}, "aspect_ratio": {"actual": 0.85, "expected": 0.37, "deviation_percent": 130.6}, "recommendations": ["⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "🔄 Image quality insufficient. Please capture again with better camera/scanner settings."]}, "blur": {"metrics": {"laplacian_variance": 5331.33, "is_blurry": false, "blur_score": 100.0, "blur_level": "Very Sharp"}, "motion_blur": {"detected": "False", "score": 7.125202497909699, "direction": "horizontal"}, "focus_distribution": {"sharp_areas_percent": 67.3, "uniform_sharpness": false}, "recommendations": ["✅ Image sharpness is excellent."]}, "glare": {"exposure_metrics": {"mean_brightness": 241.3, "overexposed_percent": "68.21", "is_overexposed": "True", "contrast_ratio": 0.15}, "glare_analysis": {"glare_score": 0, "glare_level": "Severe", "num_glare_spots": 1, "glare_coverage_percent": 67.14, "glare_patterns": {"type": "flash", "description": "Camera flash reflection detected"}}, "affected_regions": [{"bbox": ["0", "0", "624", "736"], "center": [314, 401], "area": "308350", "intensity": 242.82662259615384}], "recommendations": ["📷 Image is overexposed. Reduce camera exposure or lighting.", "🔦 Disable camera flash to avoid reflections.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "🎨 Low contrast detected. Ensure even lighting across document."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 60.0, "weight": 0.2, "contribution": 12.0}, "blur": {"score": 100.0, "weight": 0.25, "contribution": 25.0}, "glare": {"score": 0, "weight": 0.2, "contribution": 0.0}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": true, "quality_score": 72.0, "quality_level": "Acceptable", "main_issues": ["Severe glare detected"], "top_recommendations": ["📱 This is a digital_document - physical document checks have been adjusted.", "⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "⚠️ Significant glare detected. Adjust lighting and recapture."]}