{"image_path": "expense_files\\netta_austria_1.png", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.83, "image_subtype": "mobile_app_screenshot", "indicators": {"low_color_variety": true, "matches_common_resolution": false, "has_regular_edges": "True", "has_uniform_background": true, "has_many_straight_lines": true, "low_histogram_entropy": "True"}, "metadata": {"unique_colors": 610, "edge_density": 0.035, "histogram_entropy": "0.59"}}, "timestamp": "2025-07-16T12:36:10.507884", "processing_time_seconds": 1.27, "overall_assessment": {"score": 95.0, "level": "Excellent", "pass_fail": true, "issues_summary": [], "recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "📈 Consider rescanning at 300 DPI or higher for better OCR accuracy.", "✅ Image sharpness is excellent.", "✅ Digital image with clean background.", "✅ Digital screenshot - no physical boundaries to check."]}, "detailed_results": {"resolution": {"dimensions": {"width": 837, "height": 943, "megapixels": 0.79}, "dpi": {"horizontal": 268.0, "vertical": 111.0, "average": 189.0}, "quality": {"score": 80.0, "level": "Digital Quality", "meets_ocr_requirements": false}, "aspect_ratio": {"actual": 0.89, "expected": 0.37, "deviation_percent": 141.4}, "recommendations": ["📈 Consider rescanning at 300 DPI or higher for better OCR accuracy."]}, "blur": {"metrics": {"laplacian_variance": 4442.65, "is_blurry": false, "blur_score": 100.0, "blur_level": "Very Sharp"}, "motion_blur": {"detected": "False", "score": 9.033897510550608, "direction": "horizontal"}, "focus_distribution": {"sharp_areas_percent": 42.4, "uniform_sharpness": false}, "recommendations": ["✅ Image sharpness is excellent."]}, "glare": {"exposure_metrics": {"mean_brightness": 243.4, "overexposed_percent": "94.01", "is_overexposed": "True", "contrast_ratio": 0.2}, "glare_analysis": {"glare_score": 95.0, "glare_level": "None (Digital)", "num_glare_spots": 20, "glare_coverage_percent": 93.76, "glare_patterns": {"type": "flash", "description": "Camera flash reflection detected"}}, "affected_regions": [{"bbox": ["0", "0", "837", "943"], "center": [418, 453], "area": "727259", "intensity": 244.2117292608176}, {"bbox": ["305", "670", "17", "17"], "center": [313, 678], "area": "168", "intensity": 157.68512110726644}, {"bbox": ["455", "670", "20", "27"], "center": [462, 683], "area": "198", "intensity": 134.28703703703704}, {"bbox": ["521", "670", "16", "17"], "center": [528, 678], "area": "151", "intensity": 151.60294117647058}, {"bbox": ["330", "695", "152", "173"], "center": [415, 777], "area": "3811", "intensity": 132.44261484636448}], "recommendations": ["✅ Digital image with clean background."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 80.0, "weight": 0.2, "contribution": 16.0}, "blur": {"score": 100.0, "weight": 0.25, "contribution": 25.0}, "glare": {"score": 95.0, "weight": 0.2, "contribution": 19.0}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": true, "quality_score": 95.0, "quality_level": "Excellent", "main_issues": [], "top_recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "📈 Consider rescanning at 300 DPI or higher for better OCR accuracy.", "✅ Image sharpness is excellent."]}